name: Deploy WagTail to VPS

on:
  # push:
  #   branches:
  #     - main
  release:
    type: [published]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Deploy via SSH
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd projects/wagtail-portfolio
            git pull origin main
            docker compose up -d --build
            touch ../rob_$(date +%Y%m%d_%H%M%S).txt

