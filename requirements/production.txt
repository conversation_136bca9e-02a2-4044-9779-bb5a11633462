-r base.txt
elasticsearch==5.5.3
# Additional dependencies for Heroku, AWS, and Google Cloud deployment
# uwsgi>=2.0.17,<2.1
psycopg[binary]>=3.2.2,<3.3
whitenoise==6.6.0
boto3>=1.37,<1.38
google-cloud-storage==2.13.0
django-storages==1.14.2
# For retrieving credentials and signing requests to Elasticsearch
aws-requests-auth==0.4.3
django-redis==5.4.0
django-basic-auth-ip-whitelist>=0.7,<0.8
python-decouple==3.8
gunicorn==20.0.4