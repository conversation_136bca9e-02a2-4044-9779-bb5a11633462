
services:

  redis:
    container_name: wagtail_redis
    restart: always
    image: redis:6.2
    expose:
      - '6379'

  web:
    container_name: wagtail_web
    restart: always
    environment:
      DJANGO_SECRET_KEY: "${DJANGO_SECRET_KEY}"
      DATABASE_URL: "${DATABASE_URL}"
      REDIS_URL: redis://redis
      DJANGO_SETTINGS_MODULE: bakerydemo.settings.dev
    build:
      context: .
      dockerfile: ./Dockerfile
    volumes:
      - ./bakerydemo:/code/bakerydemo
    links:
      - redis:redis
    ports:
      - '8000:8000'
    env_file:
      - .env
    #entrypoint: ["/bin/bash", "/app/entrypoint.sh"]
    tty: true

    depends_on:
      - redis

    networks:
      - docker-db_rede
      - n8n-shared
networks:
  n8n-shared:
    external: true

  docker-db_rede:
    external: true
