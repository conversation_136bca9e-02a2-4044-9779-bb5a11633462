version: "3.7"

services:

  db:
    image: postgres:16
    container_name: wagtail_db
    env_file:
      - .env
    expose:
      - '5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5438:5432"

  redis:
    container_name: wagtail_redis
    image: redis:6.2
    expose:
      - '6379'

  web:
    container_name: wagtail_web
    env_file:
      - .env
    environment:
      DJANGO_SECRET_KEY: "${DJANGO_SECRET_KEY}"
      # DATABASE_URL: "${DATABASE_URL}"
      REDIS_URL: redis://redis
      DJANGO_SETTINGS_MODULE: bakerydemo.settings.dev
    build:
      context: .
      dockerfile: ./Dockerfile-dev
    volumes:
      - ./bakerydemo:/code/bakerydemo
    links:
      - db:db
      - redis:redis
    ports:
      - '8000:8000'

    #entrypoint: ["/bin/bash", "/app/entrypoint.sh"]
    tty: true

      
    depends_on:
      - db
      - redis

  nginx:
    image: nginx:alpine
    container_name: wagtail_nginx
    ports:
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - web
volumes:
  postgres_data:
